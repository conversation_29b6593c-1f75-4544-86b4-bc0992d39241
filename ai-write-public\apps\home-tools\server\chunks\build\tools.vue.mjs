import { _ as __nuxt_component_0$1 } from './client-only.mjs';
import { ref, computed, withAsyncContext, watch, mergeProps, unref, useSSRContext, resolveComponent, withCtx, createTextVNode, toDisplayString, isRef, createVNode } from 'vue';
import { ssrRenderAttrs, ssrRenderStyle, ssrRenderAttr, ssrInterpolate, ssrRenderComponent } from 'vue/server-renderer';
import { useRouter } from 'vue-router';
import cookie from 'js-cookie';
import { e as useI18n, f as useRoute, g as useCookie, i as useRequestEvent, k as useRequestHeaders, _ as __nuxt_component_1 } from './server.mjs';
import { g as getAppLangs, a as getPackageByKey, h as getAppByUuid } from './requestDify.mjs';
import { u as useAsyncData } from './asyncData.mjs';
import { _ as _export_sfc } from './_plugin-vue_export-helper.mjs';
import { a as appTypes } from './commonJs.mjs';
import { c as customerService } from './index.vue3.mjs';
import '../_/nitro.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'node:fs';
import 'node:url';
import 'consola';
import 'node:path';
import 'node:crypto';
import 'element-plus';
import 'axios';
import './qrcode.png.mjs';

const _sfc_main$1 = {
  __name: "index",
  __ssrInlineRender: true,
  props: {
    subStatusDetail: {
      type: Object,
      default: () => {
        return {};
      }
    }
  },
  emits: ["getAppLang", "getAppLang", "subScript"],
  async setup(__props, { emit: __emit }) {
    let __temp, __restore;
    useRouter();
    const { setLocale, locale } = useI18n();
    const props = __props;
    const subStatusDetail = ref(props.subStatusDetail);
    const avatar = ref("");
    const userInfo = ref(null);
    const isIncludeTool = ref(false);
    const selectedLanguage = ref("");
    const langs = ref([]);
    const hrefUrl = ref("");
    ref(null);
    ref();
    ref();
    computed(() => "https://www.medsci.cn/");
    const userInfoCookie = computed(() => cookie.get("userInfo"));
    const checkCookie = () => {
      var _a;
      const newVal = cookie.get("userInfo");
      if (newVal !== JSON.stringify(userInfo.value)) {
        userInfo.value = newVal ? JSON.parse(newVal) : null;
        avatar.value = ((_a = userInfo.value) == null ? void 0 : _a.avatar) || "https://img.medsci.cn/web/img/user_icon.png";
      }
    };
    const { data: res } = ([__temp, __restore] = withAsyncContext(async () => useAsyncData("languages", async () => {
      var _a;
      const route = useRoute();
      const cookie2 = useCookie("userInfo");
      const ai_apps_lang = useCookie("ai_apps_lang");
      const event = useRequestEvent();
      userInfo.value = cookie2.value || null;
      avatar.value = ((_a = userInfo.value) == null ? void 0 : _a.avatar) || "https://img.medsci.cn/web/img/user_icon.png";
      const res2 = await getAppLangs(event);
      langs.value = res2.filter((item) => !item.status).map((item) => ({ name: item.value, value: item.remark }));
      selectedLanguage.value = ai_apps_lang.value;
      isIncludeTool.value = route.path.includes("/tool");
      return null;
    })), __temp = await __temp, __restore(), __temp);
    watch(userInfoCookie, (newVal) => {
      console.log("userInfo cookie changed:", newVal);
      checkCookie();
    });
    return (_ctx, _push, _parent, _attrs) => {
      var _a, _b, _c, _d, _e;
      const _component_client_only = __nuxt_component_0$1;
      _push(`<div${ssrRenderAttrs(mergeProps({ class: "header ms-header-media" }, _attrs))} data-v-ff120108><div class="ms-header" data-v-ff120108><div class="wrapper" data-v-ff120108><div class="main-menu-placeholder wrapper clearfix" style="${ssrRenderStyle({ "height": "56px", "display": "block !important" })}" data-v-ff120108><div class="ms-header-img" data-v-ff120108><a${ssrRenderAttr("href", hrefUrl.value)} title="梅斯小智" data-v-ff120108><img src="https://img.medsci.cn/202412/8a5663cb16a84c45b9c8c5db4eda0075-6pyGtTcj0asd.png" alt="" data-v-ff120108></a></div><div id="main-menu" class="ms-header-nav" data-v-ff120108><div class="header-top header-user" id="user-info-header" data-v-ff120108><ul data-v-ff120108>`);
      if (unref(locale) == "zh-CN") {
        _push(`<li data-v-ff120108><div class="m_font change_lang m_none h-full" data-v-ff120108><button type="primary" style="${ssrRenderStyle({ "background-image": "linear-gradient(270deg, #FDE39B 0%, #F9D07A 88%)" })}" class="px-[15px] py-[4px] flex items-center h-[28px] mr-[8px] rounded border-none text-xs text-[#614018]" data-v-ff120108>${ssrInterpolate(subStatusDetail.value.packageType == "免费" ? "升级订阅" : subStatusDetail.value.packageType == "连续包月" || subStatusDetail.value.packageType == "连续包年" ? "修改订阅" : "订阅")}</button></div><div class="m_font change_lang pc_none" data-v-ff120108><button type="primary" style="${ssrRenderStyle({ "background-image": "linear-gradient(270deg, #FDE39B 0%, #F9D07A 88%)" })}" class="px-[14px] py-[4px] rounded-[8px] flex items-center mr-[8px] h-[28px] rounded border-none text-xs text-[#614018] whitespace-nowrap" data-v-ff120108>${ssrInterpolate(subStatusDetail.value.packageType == "免费" ? "升级订阅" : subStatusDetail.value.packageType == "连续包月" || subStatusDetail.value.packageType == "连续包年" ? "修改订阅" : "订阅")}</button></div></li>`);
      } else {
        _push(`<!---->`);
      }
      _push(`<li data-v-ff120108><div class="flex items-center h-full" data-v-ff120108><a class="backImg" target="_top" title="梅斯小智"${ssrRenderAttr("href", hrefUrl.value)} data-v-ff120108>${ssrInterpolate(_ctx.$t("tool.backtohome"))}</a></div></li><li class="index-user-img index-user-img_left" data-v-ff120108>`);
      if (!isIncludeTool.value) {
        _push(`<div class="change_lang" data-v-ff120108><span class="current_lang" data-v-ff120108>${ssrInterpolate((_a = langs.value.filter((item) => item.value == selectedLanguage.value)[0]) == null ? void 0 : _a.name)}</span><span class="ms-link" data-v-ff120108>${ssrInterpolate(_ctx.$t("market.switch"))}</span></div>`);
      } else {
        _push(`<!---->`);
      }
      _push(`<div class="ms-dropdown-menu" data-v-ff120108><div class="new-header-avator-pop" id="new-header-avator" data-v-ff120108><div class="new-header-bottom" style="${ssrRenderStyle({ "padding": "0" })}" data-v-ff120108>`);
      _push(ssrRenderComponent(_component_client_only, null, {}, _parent));
      _push(`</div></div></div></li>`);
      if (!((_b = userInfo.value) == null ? void 0 : _b.userId)) {
        _push(`<li class="index-user-img_right" data-v-ff120108><a href="javascript: void(0)" class="ms-link" data-v-ff120108>${ssrInterpolate(_ctx.$t("market.login"))}</a></li>`);
      } else {
        _push(`<li class="index-user-img" data-v-ff120108><a href="#" data-v-ff120108><div class="img-area" data-v-ff120108><img${ssrRenderAttr(
          "src",
          avatar.value ? avatar.value + "?t=" + Date.now() : "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR42mNkYAAAAAYAAjCB0C8AAAAASUVORK5CYII="
        )} alt="" data-v-ff120108></div></a><div class="ms-dropdown-menu" data-v-ff120108><div class="new-header-avator-pop" id="new-header-avator" data-v-ff120108><a class="new-header-exit ms-statis" ms-statis="logout" href="#" data-v-ff120108>${ssrInterpolate(_ctx.$t("market.logout"))}</a><div class="new-header-top" data-v-ff120108><div class="new-header-info" data-v-ff120108><img class="new-header-avatar"${ssrRenderAttr(
          "src",
          avatar.value ? avatar.value + "?t=" + Date.now() : "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR42mNkYAAAAAYAAjCB0C8AAAAASUVORK5CYII="
        )} alt="" data-v-ff120108><div class="new-header-name" data-v-ff120108><span data-v-ff120108>${ssrInterpolate(((_c = userInfo.value) == null ? void 0 : _c.realName) ? (_d = userInfo.value) == null ? void 0 : _d.realName : (_e = userInfo.value) == null ? void 0 : _e.userName)}</span></div></div></div></div></div></li>`);
      }
      _push(`</ul></div></div></div></div></div></div>`);
    };
  }
};
const _sfc_setup$1 = _sfc_main$1.setup;
_sfc_main$1.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("components/headerToolNav/index.vue");
  return _sfc_setup$1 ? _sfc_setup$1(props, ctx) : void 0;
};
const __nuxt_component_0 = /* @__PURE__ */ _export_sfc(_sfc_main$1, [["__scopeId", "data-v-ff120108"]]);

const _sfc_main = {
  __name: "tools",
  __ssrInlineRender: true,
  async setup(__props) {
    var _a;
    let __temp, __restore;
    const { locale } = useI18n();
    const route = useRoute();
    ref([]);
    ref(null);
    const nodeInfo = ref(null);
    const visible = ref(false);
    const clientHeight = ref(0);
    const subStatusDetail = ref(null);
    const isZH = ref(false);
    const defaultLanguage = "en";
    ((_a = route.params) == null ? void 0 : _a.lang) || defaultLanguage;
    const hrefUrl = ref("");
    const pageRef = ref(null);
    const { data: appListData } = ([__temp, __restore] = withAsyncContext(async () => useAsyncData("getAppList", async () => {
      useRequestHeaders();
      const route2 = useRoute();
      const event = useRequestEvent();
      let PackageByKey;
      if (locale.value == "zh-CN") {
        PackageByKey = await getPackageByKey(event);
        console.log(PackageByKey);
      }
      const res = await getAppByUuid(route2.params.appUuid, event);
      if (res) {
        return [res, PackageByKey];
      }
      return null;
    }, { server: true })), __temp = await __temp, __restore(), __temp);
    nodeInfo.value = appListData.value[0];
    subStatusDetail.value = appListData.value[1];
    const onDialog = () => {
      visible.value = true;
    };
    const subScripts = () => {
      pageRef.value.pageRef.subScript();
    };
    const isZHChange = (val) => {
      isZH.value = val;
    };
    return (_ctx, _push, _parent, _attrs) => {
      var _a2, _b;
      const _component_HeaderToolNav = __nuxt_component_0;
      const _component_el_button = resolveComponent("el-button");
      const _component_NuxtPage = __nuxt_component_1;
      const _component_el_dialog = resolveComponent("el-dialog");
      _push(`<div${ssrRenderAttrs(_attrs)} data-v-38ef4d5b>`);
      _push(ssrRenderComponent(_component_HeaderToolNav, {
        onIsZHChange: isZHChange,
        subStatusDetail: unref(subStatusDetail),
        onSubScript: subScripts
      }, null, _parent));
      if (unref(isZH)) {
        _push(ssrRenderComponent(customerService, null, null, _parent));
      } else {
        _push(`<!---->`);
      }
      _push(`<div class="h-full flex p-6" style="${ssrRenderStyle({ height: `${unref(clientHeight)}px` })}" data-v-38ef4d5b><main class="flex-1 flex flex-col" data-v-38ef4d5b><div data-v-38ef4d5b>`);
      if (unref(nodeInfo)) {
        _push(`<div class="text-gray-500 mb-2" data-v-38ef4d5b><a class="cursor-pointer hover:text-[#5298FF]"${ssrRenderAttr("href", unref(hrefUrl))} title="梅斯小智" data-v-38ef4d5b>${ssrInterpolate(_ctx.$t("tool.home"))}</a><span class="line" data-v-38ef4d5b>${ssrInterpolate("/")}</span><span data-v-38ef4d5b>${ssrInterpolate(unref(nodeInfo).appType ? _ctx.$t(`${unref(appTypes)[unref(nodeInfo).appType]}`) : "")}</span><span class="line" data-v-38ef4d5b>${ssrInterpolate("/")}</span><span data-v-38ef4d5b>${ssrInterpolate(unref(nodeInfo).appName)}</span></div>`);
      } else {
        _push(`<!---->`);
      }
      _push(`<div class="flex items-center my-2" data-v-38ef4d5b><img class="w-[38px] h-[38px] mr-4"${ssrRenderAttr("src", ((_a2 = unref(nodeInfo)) == null ? void 0 : _a2.appIcon) || "https://img.medsci.cn/web/prod/img/user_icon.png")} alt="这是icon" data-v-38ef4d5b><h3 class="text-lg font-bold text-[#666666] text-dark-200 mr-4" data-v-38ef4d5b>${ssrInterpolate((_b = unref(nodeInfo)) == null ? void 0 : _b.appName)}</h3>`);
      _push(ssrRenderComponent(_component_el_button, {
        plain: "",
        size: "small",
        onClick: onDialog
      }, {
        default: withCtx((_, _push2, _parent2, _scopeId) => {
          if (_push2) {
            _push2(`${ssrInterpolate(_ctx.$t("tool.intro"))}`);
          } else {
            return [
              createTextVNode(toDisplayString(_ctx.$t("tool.intro")), 1)
            ];
          }
        }),
        _: 1
      }, _parent));
      _push(`</div></div><div class="flex-1" data-v-38ef4d5b>`);
      _push(ssrRenderComponent(_component_NuxtPage, {
        "current-item": unref(nodeInfo),
        subStatusDetail: unref(subStatusDetail),
        ref_key: "pageRef",
        ref: pageRef
      }, null, _parent));
      _push(`</div></main>`);
      _push(ssrRenderComponent(_component_el_dialog, {
        modelValue: unref(visible),
        "onUpdate:modelValue": ($event) => isRef(visible) ? visible.value = $event : null,
        "show-close": false,
        width: "500"
      }, {
        header: withCtx((_, _push2, _parent2, _scopeId) => {
          var _a3, _b2, _c, _d;
          if (_push2) {
            _push2(`<div class="flex items-center my-2" data-v-38ef4d5b${_scopeId}><img class="w-[38px] h-[38px] mr-4"${ssrRenderAttr("src", ((_a3 = unref(nodeInfo)) == null ? void 0 : _a3.appIcon) || "https://img.medsci.cn/web/prod/img/user_icon.png")} alt="这是icon" data-v-38ef4d5b${_scopeId}><div class="text-xl font-bold text-dark-200" data-v-38ef4d5b${_scopeId}>${ssrInterpolate((_b2 = unref(nodeInfo)) == null ? void 0 : _b2.appName)}</div></div>`);
          } else {
            return [
              createVNode("div", { class: "flex items-center my-2" }, [
                createVNode("img", {
                  class: "w-[38px] h-[38px] mr-4",
                  src: ((_c = unref(nodeInfo)) == null ? void 0 : _c.appIcon) || "https://img.medsci.cn/web/prod/img/user_icon.png",
                  alt: "这是icon"
                }, null, 8, ["src"]),
                createVNode("div", { class: "text-xl font-bold text-dark-200" }, toDisplayString((_d = unref(nodeInfo)) == null ? void 0 : _d.appName), 1)
              ])
            ];
          }
        }),
        default: withCtx((_, _push2, _parent2, _scopeId) => {
          var _a3, _b2;
          if (_push2) {
            _push2(`<div class="text-dark-500 mb-6" data-v-38ef4d5b${_scopeId}>${ssrInterpolate((_a3 = unref(nodeInfo)) == null ? void 0 : _a3.appDescription)}</div><div class="flex justify-center" data-v-38ef4d5b${_scopeId}>`);
            _push2(ssrRenderComponent(_component_el_button, {
              onClick: ($event) => visible.value = false
            }, {
              default: withCtx((_2, _push3, _parent3, _scopeId2) => {
                if (_push3) {
                  _push3(`我知道了`);
                } else {
                  return [
                    createTextVNode("我知道了")
                  ];
                }
              }),
              _: 1
            }, _parent2, _scopeId));
            _push2(`</div>`);
          } else {
            return [
              createVNode("div", { class: "text-dark-500 mb-6" }, toDisplayString((_b2 = unref(nodeInfo)) == null ? void 0 : _b2.appDescription), 1),
              createVNode("div", { class: "flex justify-center" }, [
                createVNode(_component_el_button, {
                  onClick: ($event) => visible.value = false
                }, {
                  default: withCtx(() => [
                    createTextVNode("我知道了")
                  ]),
                  _: 1
                }, 8, ["onClick"])
              ])
            ];
          }
        }),
        _: 1
      }, _parent));
      _push(`</div></div>`);
    };
  }
};
const _sfc_setup = _sfc_main.setup;
_sfc_main.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("layouts/tools.vue");
  return _sfc_setup ? _sfc_setup(props, ctx) : void 0;
};
const tools = /* @__PURE__ */ _export_sfc(_sfc_main, [["__scopeId", "data-v-38ef4d5b"]]);

export { tools as default };
//# sourceMappingURL=tools.vue.mjs.map
