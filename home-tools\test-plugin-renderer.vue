<template>
  <div class="test-page p-6 max-w-6xl mx-auto">
    <h1 class="text-3xl font-bold mb-6">插件化 AiResponseRenderer 测试</h1>
    
    <div class="mb-6">
      <label class="block text-sm font-medium mb-2">选择测试内容：</label>
      <select 
        v-model="selectedTest" 
        class="border border-gray-300 rounded px-3 py-2 w-full max-w-xs"
      >
        <option value="codeHighlight">代码高亮测试</option>
        <option value="comprehensive">综合功能测试</option>
        <option value="comparison">与 TSX 版本对比</option>
      </select>
    </div>

    <div class="grid grid-cols-1 xl:grid-cols-2 gap-6">
      <!-- 原始内容 -->
      <div class="space-y-4">
        <h2 class="text-xl font-semibold">原始 Markdown</h2>
        <textarea 
          v-model="testContents[selectedTest]"
          class="w-full h-96 p-3 border border-gray-300 rounded font-mono text-sm resize-none"
          readonly
        />
      </div>

      <!-- 渲染结果 -->
      <div class="space-y-4">
        <h2 class="text-xl font-semibold">插件化渲染结果</h2>
        <div class="border border-gray-300 rounded p-4 bg-white h-96 overflow-auto">
          <AiResponseRenderer :content="testContents[selectedTest]" />
        </div>
      </div>
    </div>

    <!-- 插件说明 -->
    <div class="mt-8 p-4 bg-green-50 rounded-lg">
      <h3 class="text-lg font-semibold mb-2 text-green-800">使用的插件</h3>
      <ul class="text-sm space-y-1 text-green-700">
        <li>📦 <strong>marked</strong> - 核心 Markdown 解析器</li>
        <li>🎨 <strong>marked-highlight</strong> - 代码高亮插件</li>
        <li>💡 <strong>highlight.js</strong> - 语法高亮库</li>
        <li>📊 <strong>mermaid</strong> - 图表渲染 (CDN)</li>
        <li>🧮 <strong>katex</strong> - 数学公式渲染 (CDN)</li>
      </ul>
    </div>

    <div class="mt-4 p-4 bg-blue-50 rounded-lg">
      <h3 class="text-lg font-semibold mb-2 text-blue-800">特性对比</h3>
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
        <div>
          <h4 class="font-medium text-blue-700 mb-2">TSX 版本 (React)</h4>
          <ul class="space-y-1 text-blue-600">
            <li>• react-markdown</li>
            <li>• remark-gfm</li>
            <li>• rehype-raw</li>
            <li>• react-syntax-highlighter</li>
            <li>• react-katex</li>
          </ul>
        </div>
        <div>
          <h4 class="font-medium text-blue-700 mb-2">Vue 版本 (当前)</h4>
          <ul class="space-y-1 text-blue-600">
            <li>• marked</li>
            <li>• marked-highlight</li>
            <li>• highlight.js</li>
            <li>• mermaid (CDN)</li>
            <li>• katex (CDN)</li>
          </ul>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import AiResponseRenderer from './components/AiResponseRenderer/index.vue'

const selectedTest = ref('codeHighlight')

const testContents = reactive({
  codeHighlight: `# 代码高亮测试

## JavaScript 代码

\`\`\`javascript
// ES6 类和异步函数
class ApiClient {
  constructor(baseUrl) {
    this.baseUrl = baseUrl;
    this.headers = {
      'Content-Type': 'application/json'
    };
  }

  async get(endpoint) {
    try {
      const response = await fetch(\`\${this.baseUrl}\${endpoint}\`, {
        method: 'GET',
        headers: this.headers
      });
      
      if (!response.ok) {
        throw new Error(\`HTTP error! status: \${response.status}\`);
      }
      
      return await response.json();
    } catch (error) {
      console.error('API request failed:', error);
      throw error;
    }
  }
}

// 使用示例
const client = new ApiClient('https://api.example.com');
client.get('/users').then(data => console.log(data));
\`\`\`

## Python 代码

\`\`\`python
import asyncio
import aiohttp
from typing import List, Dict, Optional

class AsyncApiClient:
    def __init__(self, base_url: str):
        self.base_url = base_url
        self.session: Optional[aiohttp.ClientSession] = None
    
    async def __aenter__(self):
        self.session = aiohttp.ClientSession()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()
    
    async def get_users(self) -> List[Dict]:
        """获取用户列表"""
        if not self.session:
            raise RuntimeError("Session not initialized")
        
        async with self.session.get(f"{self.base_url}/users") as response:
            response.raise_for_status()
            return await response.json()

# 使用示例
async def main():
    async with AsyncApiClient("https://api.example.com") as client:
        users = await client.get_users()
        print(f"Found {len(users)} users")

if __name__ == "__main__":
    asyncio.run(main())
\`\`\`

## CSS 代码

\`\`\`css
/* 现代 CSS Grid 布局 */
.container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  padding: 2rem;
  max-width: 1200px;
  margin: 0 auto;
}

.card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  padding: 2rem;
  color: white;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.card:hover {
  transform: translateY(-5px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

@media (max-width: 768px) {
  .container {
    grid-template-columns: 1fr;
    padding: 1rem;
  }
}
\`\`\`

## 内联代码

在 JavaScript 中，可以使用 \`const\` 和 \`let\` 声明变量，\`async/await\` 处理异步操作。

Python 中的 \`async with\` 语句用于异步上下文管理器。`,

  comprehensive: `# 综合功能测试

这个测试展示了与 TSX 版本相同的功能。

## 文本格式

**粗体文本**、*斜体文本*、***粗斜体***、~~删除线~~

## Emoji 支持

基础表情：:) :( :D ;) <3 </3

原生 Emoji：🎉 🚀 💡 ⭐ 🔥

省略号转换：这是一个很长的句子...

波浪号：很好~ 不错~

## 列表

### 无序列表
- 第一项
- 第二项
  - 嵌套项 1
  - 嵌套项 2
- 第三项

### 有序列表
1. 第一步
2. 第二步
3. 第三步

## 引用块

> 这是一个引用块。
> 
> 支持多行内容和 **格式化文本**。

## 表格

| 功能 | TSX 版本 | Vue 版本 | 状态 |
|------|----------|----------|------|
| Markdown 解析 | react-markdown | marked | ✅ |
| 代码高亮 | react-syntax-highlighter | highlight.js | ✅ |
| 数学公式 | react-katex | katex | ✅ |
| 图表 | mermaid | mermaid | ✅ |

## 链接和图片

[GitHub](https://github.com)

![示例图片](https://via.placeholder.com/300x200)

---

## 特殊内容

### Mermaid 图表

\`\`\`mermaid
graph TD
    A[开始] --> B{判断条件}
    B -->|是| C[执行操作]
    B -->|否| D[跳过]
    C --> E[结束]
    D --> E
\`\`\`

### LaTeX 数学公式

\`\`\`latex
E = mc^2
\`\`\`

行内公式：$f(x) = x^2 + 2x + 1$

块级公式：
$$\\int_{-\\infty}^{\\infty} e^{-x^2} dx = \\sqrt{\\pi}$$`,

  comparison: `# TSX vs Vue 版本对比

## 相同的输出效果

这两个版本应该产生**完全相同**的渲染效果：

### 代码块
\`\`\`typescript
interface User {
  id: number;
  name: string;
  email: string;
}

const users: User[] = [
  { id: 1, name: "Alice", email: "<EMAIL>" },
  { id: 2, name: "Bob", email: "<EMAIL>" }
];
\`\`\`

### 表格
| 特性 | TSX | Vue |
|------|-----|-----|
| 语法高亮 | ✅ | ✅ |
| 表格渲染 | ✅ | ✅ |
| Emoji | ✅ | ✅ |

### 格式化文本
- **粗体**
- *斜体*
- \`代码\`
- [链接](https://example.com)

### Emoji 测试
:) :( :D ;) <3 🎉 🚀

如果渲染效果一致，说明插件化实现成功！`
})
</script>

<style scoped>
.test-page {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
}

textarea {
  font-family: 'JetBrains Mono', 'Fira Code', 'Monaco', monospace;
}
</style>
