// Generated by @nuxtjs/sitemap

declare module 'nitropack' {
  interface PrerenderRoute {
    _sitemap?: import('../../node_modules/@nuxtjs/sitemap/runtime/types').SitemapUrl
  }
  interface NitroRouteRules {
    index?: boolean
    sitemap?: import('../../node_modules/@nuxtjs/sitemap/runtime/types').SitemapItemDefaults
  }
  interface NitroRouteConfig {
    index?: boolean
    sitemap?: import('../../node_modules/@nuxtjs/sitemap/runtime/types').SitemapItemDefaults
  }
  interface NitroRuntimeHooks {
    'sitemap:index-resolved': (ctx: import('../../node_modules/@nuxtjs/sitemap/runtime/types').SitemapIndexRenderCtx) => void | Promise<void>
    'sitemap:input': (ctx: import('../../node_modules/@nuxtjs/sitemap/runtime/types').SitemapInputCtx) => void | Promise<void>
    'sitemap:resolved': (ctx: import('../../node_modules/@nuxtjs/sitemap/runtime/types').SitemapRenderCtx) => void | Promise<void>
    'sitemap:output': (ctx: import('../../node_modules/@nuxtjs/sitemap/runtime/types').SitemapOutputHookCtx) => void | Promise<void>
  }
}
declare module 'vue-router' {
    interface RouteMeta {
        sitemap?: import('../../node_modules/@nuxtjs/sitemap/runtime/types').SitemapItemDefaults
    }
}

export {}
