{"compilerOptions": {"forceConsistentCasingInFileNames": true, "strict": true, "noEmit": true, "target": "ESNext", "module": "ESNext", "moduleResolution": "<PERSON><PERSON><PERSON>", "allowJs": true, "resolveJsonModule": true, "jsx": "preserve", "allowSyntheticDefaultImports": true, "jsxFactory": "h", "jsxFragmentFactory": "Fragment", "paths": {"#imports": ["./types/nitro-imports"], "~/*": ["../*"], "@/*": ["../*"], "~~/*": ["../*"], "@@/*": ["../*"], "nitropack/types": ["../node_modules/nitropack/types"], "nitropack/runtime": ["../node_modules/nitropack/runtime"], "nitropack": ["../node_modules/nitropack"], "defu": ["../node_modules/defu"], "h3": ["../node_modules/h3"], "consola": ["../node_modules/consola"], "ofetch": ["../node_modules/ofetch"], "@unhead/vue": ["../node_modules/@unhead/vue"], "@nuxt/devtools": ["../node_modules/@nuxt/devtools"], "@vue/runtime-core": ["../node_modules/@vue/runtime-core"], "@vue/compiler-sfc": ["../node_modules/@vue/compiler-sfc"], "unplugin-vue-router/client": ["../node_modules/unplugin-vue-router/client"], "@nuxt/schema": ["../node_modules/@nuxt/schema"], "nuxt": ["../node_modules/nuxt"], "vite/client": ["../node_modules/vite/client"], "#shared": ["../shared"], "assets": ["../assets"], "assets/*": ["../assets/*"], "public": ["../public"], "public/*": ["../public/*"], "#build": ["./"], "#build/*": ["./*"], "#internal/nuxt/paths": ["../node_modules/nuxt/dist/core/runtime/nitro/utils/paths"], "#sitemap": ["../node_modules/@nuxtjs/sitemap/dist/runtime"], "#sitemap/*": ["../node_modules/@nuxtjs/sitemap/dist/runtime/*"], "#site-config": ["../node_modules/nuxt-site-config/dist/runtime"], "#site-config/*": ["../node_modules/nuxt-site-config/dist/runtime/*"], "#unhead/composables": ["../node_modules/nuxt/dist/head/runtime/composables/v3"], "vue-i18n": ["../node_modules/vue-i18n/dist/vue-i18n"], "@intlify/shared": ["../node_modules/@intlify/shared/dist/shared"], "@intlify/message-compiler": ["../node_modules/@intlify/message-compiler/dist/message-compiler"], "@intlify/core-base": ["../node_modules/@intlify/core-base/dist/core-base"], "@intlify/core": ["../node_modules/@intlify/core/dist/core.node"], "@intlify/utils/h3": ["../node_modules/@intlify/utils/dist/h3"], "ufo": ["../node_modules/ufo/dist/index"], "#i18n": ["../node_modules/@nuxtjs/i18n/dist/runtime/composables/index"], "#internal-i18n-types": ["../node_modules/@nuxtjs/i18n/dist/types"], "#nuxt-i18n/logger": ["./nuxt-i18n-logger"], "#internal/nuxt-site-config": ["../node_modules/nuxt-site-config/dist/runtime/server/composable-barrel-deprecated"]}, "lib": ["esnext", "webworker", "dom.iterable"]}, "include": ["./types/nitro-nuxt.d.ts", "../node_modules/@nuxtjs/tailwindcss/runtime/server", "../node_modules/@nuxtjs/i18n/runtime/server", "../node_modules/nuxt-site-config/dist/module.mjs/runtime/server", "../node_modules/@nuxtjs/sitemap/runtime/server", "../node_modules/@nuxt/telemetry/runtime/server", "./module/nuxt-site-config.d.ts", "./module/@nuxtjs-sitemap.d.ts", "./types/nitro.d.ts", "../**/*", "../server/**/*"], "exclude": ["../node_modules", "../../node_modules", "../node_modules/nuxt/node_modules", "../node_modules/@nuxtjs/tailwindcss/node_modules", "../node_modules/@nuxtjs/i18n/node_modules", "../node_modules/nuxt-site-config/node_modules", "../node_modules/@nuxtjs/sitemap/node_modules", "../node_modules/@nuxt/telemetry/node_modules", "../dist"]}