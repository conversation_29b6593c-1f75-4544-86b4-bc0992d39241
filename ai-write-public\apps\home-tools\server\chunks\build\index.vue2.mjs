import { ref, reactive, resolveComponent, mergeProps, withCtx, createVNode, createTextVNode, createBlock, toDisplayString, openBlock, useSSRContext } from 'vue';
import { ssrRenderAttrs, ssrInterpolate, ssrRenderComponent } from 'vue/server-renderer';
import { l as login, C as Cookies } from './requestDify.mjs';
import cookie from 'js-cookie';
import { e as useI18n, f as useRoute, u as useRouter } from './server.mjs';
import { _ as _export_sfc } from './_plugin-vue_export-helper.mjs';
import 'axios';
import '../_/nitro.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'node:fs';
import 'node:url';
import 'consola';
import 'node:path';
import 'node:crypto';
import 'vue-router';
import 'element-plus';

const _sfc_main = {
  __name: "index",
  __ssrInlineRender: true,
  setup(__props) {
    var _a;
    const { t, locale } = useI18n();
    const route = useRoute();
    useRouter();
    route.params.socialType;
    route.query.authCode;
    route.query.authState;
    const signUpUrl = ref();
    const ruleForm = ref({
      email: "",
      password: ""
    });
    const ruleFormRef = ref();
    const rules = reactive({
      password: [
        { required: true, message: t(`tool.password_cannot_be_empty`), trigger: "blur" },
        { min: 8, max: 20, message: t("tool.pwdLength"), trigger: "blur" },
        {
          pattern: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[\W_])[a-zA-Z\d\W_]{8,20}$/,
          message: t(`tool.password_includes_uppercase_lowercase_numbers_and_symbols`),
          trigger: "blur"
        }
      ],
      email: [
        { required: true, message: t(`tool.email_address_cannot_be_empty`), trigger: "blur" },
        { type: "email", message: t(`tool.please_enter_a_valid_email_address`), trigger: "blur" }
      ]
    });
    cookie.get("userInfo") ? (_a = JSON.parse(cookie.get("userInfo"))) == null ? void 0 : _a.userId : "";
    const logins = async (formEl) => {
      if (!formEl) return;
      await formEl.validate((valid, fields) => {
        if (valid) {
          login(ruleForm.value).then((res) => {
            console.log(res);
            if ((res == null ? void 0 : res.token) && (res == null ? void 0 : res.htoken)) {
              Cookies.set("yudaoToken", res.token);
              localStorage.setItem("hasuraToken", res.htoken);
              localStorage.setItem("openid", res.openid);
              localStorage.setItem("socialUserId", res.socialUserId);
              localStorage.setItem("socialType", res.socialType);
              res.userInfo.userId = res.userInfo.openid;
              res.userInfo.plaintextUserId = res.userInfo.socialUserId;
              res.userInfo.token = { accessToken: res.userInfo.openid, accessTokenExpireTime: 63072e4, refreshToken: res.userInfo.openid };
              if ((void 0).origin.includes(".medsci.cn")) {
                cookie.set("userInfo", JSON.stringify(res.userInfo), { expires: 365, domain: ".medsci.cn" });
              } else if ((void 0).origin.includes(".medon.com.cn")) {
                cookie.set("userInfo", JSON.stringify(res.userInfo), { expires: 365, domain: ".medon.com.cn" });
              } else {
                cookie.set("userInfo", JSON.stringify(res.userInfo), { expires: 365 });
              }
              const redirectUrl = (void 0).sessionStorage.getItem("redirectUrl");
              if (redirectUrl) {
                (void 0).location.href = redirectUrl;
                (void 0).sessionStorage.removeItem("redirectUrl");
              } else {
                console.log("登录中...");
                (void 0).location.href = "/";
                console.log("登录中1111");
              }
            } else {
              console.error("登录失败: 未返回 token");
            }
          });
        } else {
          console.log("error submit!", fields);
        }
      });
    };
    return (_ctx, _push, _parent, _attrs) => {
      const _component_el_form = resolveComponent("el-form");
      const _component_el_form_item = resolveComponent("el-form-item");
      const _component_el_input = resolveComponent("el-input");
      const _component_el_button = resolveComponent("el-button");
      const _component_el_link = resolveComponent("el-link");
      _push(`<div${ssrRenderAttrs(mergeProps({ class: "bg-background text-foreground antialiased min-h-screen flex items-center justify-center" }, _attrs))} data-v-605ca68e><div class="cl-rootBox cl-signUp-root justify-center" data-v-605ca68e><div class="cl-cardBox cl-signUp-start" data-v-605ca68e><div class="cl-card cl-signUp-start" data-v-605ca68e><div class="cl-header" data-v-605ca68e><div data-v-605ca68e><h1 class="cl-headerTitle" data-v-605ca68e>${ssrInterpolate(_ctx.$t(`tool.login_to_MedSci_xAI`))}</h1><p class="cl-headerSubtitle" data-v-605ca68e>${ssrInterpolate(_ctx.$t(`tool.welcome_back_please_login_to_continue`))}</p></div></div><div class="cl-main" data-v-605ca68e><div class="cl-socialButtonsRoot" data-v-605ca68e><div class="cl-socialButtons" data-v-605ca68e><button class="cl-socialButtonsBlockButton cl-button cl-socialButtonsBlockButton__google" data-v-605ca68e><span class="cl-socialButtonsBlockButton-d" data-v-605ca68e><span data-v-605ca68e><img src="https://img.medsci.cn/202412/cc7c7687b4804460a85d46c629132724-vcHTUHMGIylQ.png" class="cl-socialButtonsProviderIcon cl-providerIcon cl-socialButtonsProviderIcon__google" alt="Sign in with Google" data-v-605ca68e></span><span class="cl-socialButtonsBlockButtonText" data-v-605ca68e>${ssrInterpolate(_ctx.$t(`tool.continue_with_google`))}</span></span></button></div><div class="cl-socialButtons" data-v-605ca68e><button class="cl-socialButtonsBlockButton cl-button cl-socialButtonsBlockButton__google" data-v-605ca68e><span class="cl-socialButtonsBlockButton-d" data-v-605ca68e><span data-v-605ca68e><img src="https://img.medsci.cn/202412/f9bf95f463c04d3e801aa6e97ef3d4b8-OSsmrMWR677i.png" class="cl-socialButtonsProviderIcon cl-providerIcon cl-socialButtonsProviderIcon__google" alt="Sign in with Google" data-v-605ca68e></span><span class="cl-socialButtonsBlockButtonText" data-v-605ca68e>${ssrInterpolate(_ctx.$t(`tool.continue_with_facebook`))}</span></span></button></div></div><div class="cl-dividerRow" data-v-605ca68e><div class="cl-dividerLine" data-v-605ca68e></div><p class="cl-dividerText" data-v-605ca68e>${ssrInterpolate(_ctx.$t(`tool.or`))}</p><div class="cl-dividerLine" data-v-605ca68e></div></div><div class="cl-socialButtonsRoot" data-v-605ca68e>`);
      _push(ssrRenderComponent(_component_el_form, {
        ref_key: "ruleFormRef",
        ref: ruleFormRef,
        style: { "max-width": "600px" },
        model: ruleForm.value,
        rules,
        "label-width": "auto",
        class: "demo-ruleForm",
        "label-position": "left",
        "status-icon": ""
      }, {
        default: withCtx((_, _push2, _parent2, _scopeId) => {
          if (_push2) {
            _push2(ssrRenderComponent(_component_el_form_item, {
              label: _ctx.$t(`tool.email`),
              prop: "email"
            }, {
              default: withCtx((_2, _push3, _parent3, _scopeId2) => {
                if (_push3) {
                  _push3(ssrRenderComponent(_component_el_input, {
                    modelValue: ruleForm.value.email,
                    "onUpdate:modelValue": ($event) => ruleForm.value.email = $event
                  }, null, _parent3, _scopeId2));
                } else {
                  return [
                    createVNode(_component_el_input, {
                      modelValue: ruleForm.value.email,
                      "onUpdate:modelValue": ($event) => ruleForm.value.email = $event
                    }, null, 8, ["modelValue", "onUpdate:modelValue"])
                  ];
                }
              }),
              _: 1
            }, _parent2, _scopeId));
            _push2(ssrRenderComponent(_component_el_form_item, {
              label: _ctx.$t(`tool.password`),
              prop: "password",
              style: { "padding-bottom": "20px" }
            }, {
              default: withCtx((_2, _push3, _parent3, _scopeId2) => {
                if (_push3) {
                  _push3(ssrRenderComponent(_component_el_input, {
                    modelValue: ruleForm.value.password,
                    "onUpdate:modelValue": ($event) => ruleForm.value.password = $event,
                    "show-password": true
                  }, null, _parent3, _scopeId2));
                } else {
                  return [
                    createVNode(_component_el_input, {
                      modelValue: ruleForm.value.password,
                      "onUpdate:modelValue": ($event) => ruleForm.value.password = $event,
                      "show-password": true
                    }, null, 8, ["modelValue", "onUpdate:modelValue"])
                  ];
                }
              }),
              _: 1
            }, _parent2, _scopeId));
            _push2(ssrRenderComponent(_component_el_form_item, null, {
              default: withCtx((_2, _push3, _parent3, _scopeId2) => {
                if (_push3) {
                  _push3(`<div class="cl-internal-1pnppin" data-v-605ca68e${_scopeId2}><div id="clerk-captcha" class="cl-internal-3s7k9k" data-v-605ca68e${_scopeId2}></div><div class="cl-internal-742eeh" data-v-605ca68e${_scopeId2}>`);
                  _push3(ssrRenderComponent(_component_el_button, {
                    class: "cl-formButtonPrimary cl-button cl-internal-ttumny",
                    onClick: ($event) => logins(ruleFormRef.value)
                  }, {
                    default: withCtx((_3, _push4, _parent4, _scopeId3) => {
                      if (_push4) {
                        _push4(`<span class="cl-internal-2iusy0" data-v-605ca68e${_scopeId3}>${ssrInterpolate(_ctx.$t(`tool.continue`))}<svg class="cl-buttonArrowIcon cl-internal-1c4ikgf" data-v-605ca68e${_scopeId3}><path fill="currentColor" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="m7.25 5-3.5-2.25v4.5L7.25 5Z" data-v-605ca68e${_scopeId3}></path></svg></span>`);
                      } else {
                        return [
                          createVNode("span", { class: "cl-internal-2iusy0" }, [
                            createTextVNode(toDisplayString(_ctx.$t(`tool.continue`)), 1),
                            (openBlock(), createBlock("svg", { class: "cl-buttonArrowIcon cl-internal-1c4ikgf" }, [
                              createVNode("path", {
                                fill: "currentColor",
                                stroke: "currentColor",
                                "stroke-linecap": "round",
                                "stroke-linejoin": "round",
                                "stroke-width": "1.5",
                                d: "m7.25 5-3.5-2.25v4.5L7.25 5Z"
                              })
                            ]))
                          ])
                        ];
                      }
                    }),
                    _: 1
                  }, _parent3, _scopeId2));
                  _push3(`</div></div>`);
                } else {
                  return [
                    createVNode("div", { class: "cl-internal-1pnppin" }, [
                      createVNode("div", {
                        id: "clerk-captcha",
                        class: "cl-internal-3s7k9k"
                      }),
                      createVNode("div", { class: "cl-internal-742eeh" }, [
                        createVNode(_component_el_button, {
                          class: "cl-formButtonPrimary cl-button cl-internal-ttumny",
                          onClick: ($event) => logins(ruleFormRef.value)
                        }, {
                          default: withCtx(() => [
                            createVNode("span", { class: "cl-internal-2iusy0" }, [
                              createTextVNode(toDisplayString(_ctx.$t(`tool.continue`)), 1),
                              (openBlock(), createBlock("svg", { class: "cl-buttonArrowIcon cl-internal-1c4ikgf" }, [
                                createVNode("path", {
                                  fill: "currentColor",
                                  stroke: "currentColor",
                                  "stroke-linecap": "round",
                                  "stroke-linejoin": "round",
                                  "stroke-width": "1.5",
                                  d: "m7.25 5-3.5-2.25v4.5L7.25 5Z"
                                })
                              ]))
                            ])
                          ]),
                          _: 1
                        }, 8, ["onClick"])
                      ])
                    ])
                  ];
                }
              }),
              _: 1
            }, _parent2, _scopeId));
          } else {
            return [
              createVNode(_component_el_form_item, {
                label: _ctx.$t(`tool.email`),
                prop: "email"
              }, {
                default: withCtx(() => [
                  createVNode(_component_el_input, {
                    modelValue: ruleForm.value.email,
                    "onUpdate:modelValue": ($event) => ruleForm.value.email = $event
                  }, null, 8, ["modelValue", "onUpdate:modelValue"])
                ]),
                _: 1
              }, 8, ["label"]),
              createVNode(_component_el_form_item, {
                label: _ctx.$t(`tool.password`),
                prop: "password",
                style: { "padding-bottom": "20px" }
              }, {
                default: withCtx(() => [
                  createVNode(_component_el_input, {
                    modelValue: ruleForm.value.password,
                    "onUpdate:modelValue": ($event) => ruleForm.value.password = $event,
                    "show-password": true
                  }, null, 8, ["modelValue", "onUpdate:modelValue"])
                ]),
                _: 1
              }, 8, ["label"]),
              createVNode(_component_el_form_item, null, {
                default: withCtx(() => [
                  createVNode("div", { class: "cl-internal-1pnppin" }, [
                    createVNode("div", {
                      id: "clerk-captcha",
                      class: "cl-internal-3s7k9k"
                    }),
                    createVNode("div", { class: "cl-internal-742eeh" }, [
                      createVNode(_component_el_button, {
                        class: "cl-formButtonPrimary cl-button cl-internal-ttumny",
                        onClick: ($event) => logins(ruleFormRef.value)
                      }, {
                        default: withCtx(() => [
                          createVNode("span", { class: "cl-internal-2iusy0" }, [
                            createTextVNode(toDisplayString(_ctx.$t(`tool.continue`)), 1),
                            (openBlock(), createBlock("svg", { class: "cl-buttonArrowIcon cl-internal-1c4ikgf" }, [
                              createVNode("path", {
                                fill: "currentColor",
                                stroke: "currentColor",
                                "stroke-linecap": "round",
                                "stroke-linejoin": "round",
                                "stroke-width": "1.5",
                                d: "m7.25 5-3.5-2.25v4.5L7.25 5Z"
                              })
                            ]))
                          ])
                        ]),
                        _: 1
                      }, 8, ["onClick"])
                    ])
                  ])
                ]),
                _: 1
              })
            ];
          }
        }),
        _: 1
      }, _parent));
      _push(`</div></div></div><div class="cl-footer cl-internal-4x6jej" data-v-605ca68e><div class="cl-footerAction cl-footerAction__signUp cl-internal-1rpdi70" data-v-605ca68e><span class="cl-footerActionText cl-internal-kyvqj0" data-localization-key="signUp.start.actionText" data-v-605ca68e>${ssrInterpolate(_ctx.$t(`tool.no_account_yet`))}</span>`);
      _push(ssrRenderComponent(_component_el_link, {
        href: signUpUrl.value,
        class: "cl-footerActionLink"
      }, {
        default: withCtx((_, _push2, _parent2, _scopeId) => {
          if (_push2) {
            _push2(`${ssrInterpolate(_ctx.$t("tool.signUp"))}`);
          } else {
            return [
              createTextVNode(toDisplayString(_ctx.$t("tool.signUp")), 1)
            ];
          }
        }),
        _: 1
      }, _parent));
      _push(`</div></div></div></div></div>`);
    };
  }
};
const _sfc_setup = _sfc_main.setup;
_sfc_main.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("pages/login/index.vue");
  return _sfc_setup ? _sfc_setup(props, ctx) : void 0;
};
const index = /* @__PURE__ */ _export_sfc(_sfc_main, [["__scopeId", "data-v-605ca68e"]]);

export { index as default };
//# sourceMappingURL=index.vue2.mjs.map
