import { ref, computed, withAsyncContext, mergeProps, useSSRContext } from 'vue';
import { ssrRenderAttrs, ssrInterpolate, ssrRenderAttr, ssrRenderList } from 'vue/server-renderer';
import { f as getArticleWithSEO } from './requestDify.mjs';
import { useRoute } from 'vue-router';
import { i as useRequestEvent } from './server.mjs';
import { u as useAsyncData } from './asyncData.mjs';
import { u as useHead } from './v3.mjs';
import { _ as _export_sfc } from './_plugin-vue_export-helper.mjs';
import 'axios';
import 'js-cookie';
import '../_/nitro.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'node:fs';
import 'node:url';
import 'consola';
import 'node:path';
import 'node:crypto';
import 'element-plus';
import '../routes/renderer.mjs';
import 'vue-bundle-renderer/runtime';
import 'unhead/server';
import 'unhead/utils';
import 'devalue';
import 'unhead/plugins';

const _sfc_main = {
  __name: "[id]",
  __ssrInlineRender: true,
  async setup(__props) {
    let __temp, __restore;
    const route = useRoute();
    const article = ref({});
    const seoData = ref(null);
    const loading = ref(true);
    const error = ref(false);
    const dataLoaded = ref(false);
    const recentArticles = ref([]);
    const relatedArticles = ref([]);
    const loadingQuestions = ref(true);
    const loadingRelated = ref(true);
    const decodedContent = computed(() => {
      if (!article.value.content) {
        console.log("文章内容为空");
        return "";
      }
      console.log("处理文章内容:", article.value.content.substring(0, 50) + "...");
      const content = article.value.content;
      const decoded = content.replace(/&lt;/g, "<").replace(/&gt;/g, ">").replace(/&amp;/g, "&").replace(/&ldquo;/g, '"').replace(/&rdquo;/g, '"').replace(/&hellip;/g, "...").replace(/&nbsp;/g, " ");
      console.log("解码后的内容:", decoded.substring(0, 50) + "...");
      return decoded;
    });
    const parseSeoData = (jsonStr) => {
      if (!jsonStr) return null;
      console.log("解析SEO数据:", jsonStr);
      try {
        return typeof jsonStr === "string" ? JSON.parse(jsonStr) : jsonStr;
      } catch (e) {
        console.error("解析SEO数据失败:", e);
        return null;
      }
    };
    const seoTitle = computed(() => {
      var _a;
      if (!((_a = seoData.value) == null ? void 0 : _a.seo_title)) {
        console.log("未找到SEO标题, 使用文章标题:", article.value.title);
        return article.value.title;
      }
      const titleData = parseSeoData(seoData.value.seo_title);
      console.log("解析后的SEO标题数据:", titleData);
      return (titleData == null ? void 0 : titleData.primary) || article.value.title;
    });
    const seoDescription = computed(() => {
      var _a;
      if (!((_a = seoData.value) == null ? void 0 : _a.seo_description)) {
        console.log("未找到SEO描述, 使用文章摘要:", article.value.summary);
        return article.value.summary;
      }
      const descData = parseSeoData(seoData.value.seo_description);
      console.log("解析后的SEO描述数据:", descData);
      return (descData == null ? void 0 : descData.core) || article.value.summary;
    });
    const seoKeywordsList = computed(() => {
      var _a;
      if (!((_a = seoData.value) == null ? void 0 : _a.seo_description)) return [];
      try {
        const descData = parseSeoData(seoData.value.seo_description);
        console.log("解析后的SEO描述数据(keywords):", descData == null ? void 0 : descData.keywords);
        const keywords = Array.isArray(descData == null ? void 0 : descData.keywords) ? descData.keywords : [];
        return keywords.filter((keyword) => keyword !== "梅斯医学");
      } catch (e) {
        console.error("解析SEO关键词失败:", e);
        return [];
      }
    });
    const seoKeywords = computed(() => {
      if (seoKeywordsList.value.length > 0) {
        return seoKeywordsList.value.join(",");
      }
      return article.value.articleKeyword;
    });
    const { data } = ([__temp, __restore] = withAsyncContext(async () => useAsyncData(
      "articleData",
      async (nuxtApp) => {
        try {
          const uuid = route.params.id;
          const event = useRequestEvent();
          console.log("请求ID:", uuid);
          let responseData;
          try {
            const axios = (await import('axios')).default;
            const baseUrl = true ? "http://localhost:48081" : "";
            const url = `${baseUrl}/ai-base/index/snapshot/getArticleWithSEO`;
            console.log("发送请求到:", url);
            const response = await axios.get(url, {
              params: { id: uuid }
            });
            console.log("请求状态:", response.status);
            if (response.status === 200 && response.data && response.data.code === 0) {
              responseData = response.data.data;
              console.log("获取到响应数据");
            } else {
              throw new Error("响应格式错误");
            }
          } catch (axiosErr) {
            console.error("直接请求失败，尝试API函数:", axiosErr.message);
            responseData = await getArticleWithSEO(uuid, event);
          }
          console.log("获取到的响应:", responseData);
          if (responseData && responseData.recentArticles) {
            console.log("设置最新文章数据，数量:", responseData.recentArticles.length);
            recentArticles.value = responseData.recentArticles;
          } else {
            console.error("响应中缺少recentArticles数据");
          }
          if (responseData && responseData.relatedArticles) {
            console.log("设置相关文章数据，数量:", responseData.relatedArticles.length);
            relatedArticles.value = responseData.relatedArticles;
          } else {
            console.error("响应中缺少relatedArticles数据");
          }
          loadingQuestions.value = false;
          loadingRelated.value = false;
          if (responseData && responseData.seo && responseData.article) {
            console.log("成功提取数据结构");
            dataLoaded.value = true;
            return {
              seo: responseData.seo,
              articleData: responseData.article,
              recentArticles: responseData.recentArticles || [],
              relatedArticles: responseData.relatedArticles || [],
              error: false
            };
          }
          console.error("响应数据不符合预期结构");
          return {
            seo: null,
            articleData: null,
            recentArticles: [],
            relatedArticles: [],
            error: true
          };
        } catch (err) {
          console.error("获取数据失败:", err.message);
          loadingQuestions.value = false;
          loadingRelated.value = false;
          return {
            seo: null,
            articleData: null,
            recentArticles: [],
            relatedArticles: [],
            error: true
          };
        }
      }
    )), __temp = await __temp, __restore(), __temp);
    if (data.value) {
      console.log("从useAsyncData获取的数据:", data.value);
      seoData.value = data.value.seo;
      article.value = data.value.articleData || {};
      if (data.value.recentArticles && data.value.recentArticles.length > 0) {
        recentArticles.value = data.value.recentArticles;
        console.log("设置最新文章:", recentArticles.value.length, "条");
      }
      if (data.value.relatedArticles && data.value.relatedArticles.length > 0) {
        relatedArticles.value = data.value.relatedArticles;
        console.log("设置相关文章:", relatedArticles.value.length, "条");
      }
      error.value = data.value.error;
      loading.value = false;
      loadingQuestions.value = false;
      loadingRelated.value = false;
      dataLoaded.value = true;
      console.log(
        "设置到组件的数据:",
        "seo:",
        seoData.value ? "有值" : "null",
        "article:",
        Object.keys(article.value).length ? "有值" : "空对象",
        "recentArticles:",
        recentArticles.value.length,
        "relatedArticles:",
        relatedArticles.value.length
      );
    }
    useHead({
      title: computed(() => seoTitle.value ? `${seoTitle.value} - 梅斯医学` : "文章 - 梅斯医学"),
      meta: [
        {
          name: "description",
          content: computed(() => seoDescription.value || "")
        },
        {
          name: "keywords",
          content: computed(() => seoKeywords.value || "")
        },
        // Open Graph 元标签
        {
          property: "og:title",
          content: computed(() => seoTitle.value || "")
        },
        {
          property: "og:description",
          content: computed(() => seoDescription.value || "")
        },
        {
          property: "og:image",
          content: computed(() => article.value.cover || "")
        },
        {
          property: "og:type",
          content: "article"
        }
      ]
    });
    const formatDate = (dateString) => {
      if (!dateString) return "";
      const date = new Date(dateString);
      return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, "0")}-${String(date.getDate()).padStart(2, "0")}`;
    };
    return (_ctx, _push, _parent, _attrs) => {
      _push(`<div${ssrRenderAttrs(mergeProps({ class: "page-wrapper" }, _attrs))} data-v-69782924><div class="main-container" data-v-69782924><div class="article-container" data-v-69782924>`);
      if (loading.value) {
        _push(`<div class="loading-container" data-v-69782924>加载中...</div>`);
      } else if (error.value) {
        _push(`<div class="error-container" data-v-69782924>无法加载文章</div>`);
      } else {
        _push(`<!--[--><div class="article-header" data-v-69782924><h1 class="article-title" data-v-69782924>${ssrInterpolate(article.value.title)}</h1><div class="meta-info" data-v-69782924>`);
        if (article.value.publishedTimeString) {
          _push(`<span class="publish-time" data-v-69782924>${ssrInterpolate(article.value.publishedTimeString)}</span>`);
        } else {
          _push(`<!---->`);
        }
        if (article.value.createdName) {
          _push(`<span class="author" data-v-69782924>${ssrInterpolate(article.value.createdName)}</span>`);
        } else {
          _push(`<!---->`);
        }
        if (article.value.ipAttribution) {
          _push(`<span class="location" data-v-69782924>发表于${ssrInterpolate(article.value.ipAttribution)}</span>`);
        } else {
          _push(`<!---->`);
        }
        _push(`</div></div>`);
        if (article.value.cover) {
          _push(`<div class="cover-image" data-v-69782924><img${ssrRenderAttr("src", article.value.cover)}${ssrRenderAttr("alt", article.value.title)} data-v-69782924></div>`);
        } else {
          _push(`<!---->`);
        }
        if (seoKeywordsList.value && seoKeywordsList.value.length > 0) {
          _push(`<div class="categories" data-v-69782924><!--[-->`);
          ssrRenderList(seoKeywordsList.value, (keyword, index) => {
            _push(`<span class="category" data-v-69782924>${ssrInterpolate(keyword)}</span>`);
          });
          _push(`<!--]--></div>`);
        } else {
          _push(`<!---->`);
        }
        if (article.value.summary) {
          _push(`<div class="article-summary" data-v-69782924>${ssrInterpolate(article.value.summary)}</div>`);
        } else {
          _push(`<!---->`);
        }
        if (article.value.content) {
          _push(`<div class="article-content" data-v-69782924>${decodedContent.value ?? ""}</div>`);
        } else {
          _push(`<pre data-v-69782924>${ssrInterpolate(JSON.stringify(article.value, null, 2))}</pre>`);
        }
        _push(`<!--]-->`);
      }
      _push(`</div><div class="sidebar" data-v-69782924>`);
      if (seoData.value) {
        _push(`<div class="sidebar-section tool-section" data-v-69782924><div class="section-title" data-v-69782924>工具使用</div><div class="tool-card" data-v-69782924><div class="tool-icon" data-v-69782924><img${ssrRenderAttr("src", seoData.value.app_icon || "https://www.medsci.cn/images/logo.png")} alt="工具图标" data-v-69782924></div><div class="tool-info" data-v-69782924><div class="tool-name" data-v-69782924>${ssrInterpolate(seoData.value.app_name || "工具名称")}</div><button class="use-button" data-v-69782924>使用</button></div></div></div>`);
      } else {
        _push(`<!---->`);
      }
      _push(`<div class="sidebar-section" data-v-69782924><div class="section-title" data-v-69782924>最新文章</div>`);
      if (loadingQuestions.value) {
        _push(`<div class="loading-side" data-v-69782924>加载中...</div>`);
      } else {
        _push(`<div data-v-69782924><!--[-->`);
        ssrRenderList(recentArticles.value.slice(0, 3), (item, index) => {
          _push(`<div class="article-item" data-v-69782924><div class="question-title" data-v-69782924>${ssrInterpolate(item.title)}</div><div class="question-meta" data-v-69782924>${ssrInterpolate(formatDate(item.publishedTime || item.createdTime))}</div></div>`);
        });
        _push(`<!--]-->`);
        if (!recentArticles.value || recentArticles.value.length === 0) {
          _push(`<div class="empty-data" data-v-69782924> 暂无数据 </div>`);
        } else {
          _push(`<!---->`);
        }
        _push(`</div>`);
      }
      _push(`</div><div class="sidebar-section" data-v-69782924><div class="section-title" data-v-69782924>相关文章</div>`);
      if (loadingRelated.value) {
        _push(`<div class="loading-side" data-v-69782924>加载中...</div>`);
      } else {
        _push(`<div data-v-69782924><!--[-->`);
        ssrRenderList(relatedArticles.value.slice(0, 3), (item, index) => {
          _push(`<div class="article-item" data-v-69782924><div class="related-article-title" data-v-69782924>${ssrInterpolate(item.title)}</div><div class="related-article-meta" data-v-69782924>${ssrInterpolate(formatDate(item.publishedTime || item.createdTime))}</div></div>`);
        });
        _push(`<!--]-->`);
        if (!relatedArticles.value || relatedArticles.value.length === 0) {
          _push(`<div class="empty-data" data-v-69782924> 暂无数据 </div>`);
        } else {
          _push(`<!---->`);
        }
        _push(`</div>`);
      }
      _push(`</div></div></div><div class="article-footer" data-v-69782924><div class="footer-bottom" data-v-69782924><div class="copyright" data-v-69782924> ©Copyright 2012-至今 梅斯（MedSci） </div><div class="license-info" data-v-69782924> 增值电信业务经营许可证 | 备案号 沪ICP备14018916号-1 | 互联网药品信息服务资格证书((沪)-非经营性-2020-0033) | 出版物经营许可证 </div><div class="license-info" data-v-69782924> 上海工商 | 上海网警网络110 | 网络社会征信网 | 违法和不良信息举报中心 | 信息举报中心 |违法举报：021-54485309 | 沪公网安备 31010402000380 </div><div class="footer-info" data-v-69782924> 本站旨在介绍医药健康研究进展和信息，不作为诊疗方案推荐。如需获得诊断或治疗方面指导，请前往正规医院就诊。 </div><div class="footer-info" data-v-69782924> 用户应遵守著作权法，尊重著作权人合法权益，不违法上传、存储并分享他人作品。投诉、举报、维权邮箱：<EMAIL>，或在此留言 </div></div></div></div>`);
    };
  }
};
const _sfc_setup = _sfc_main.setup;
_sfc_main.setup = (props, ctx) => {
  const ssrContext = useSSRContext();
  (ssrContext.modules || (ssrContext.modules = /* @__PURE__ */ new Set())).add("pages/article/[id].vue");
  return _sfc_setup ? _sfc_setup(props, ctx) : void 0;
};
const _id_ = /* @__PURE__ */ _export_sfc(_sfc_main, [["__scopeId", "data-v-69782924"]]);

export { _id_ as default };
//# sourceMappingURL=_id_.vue.mjs.map
