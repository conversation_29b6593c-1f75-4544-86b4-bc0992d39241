<template>
  <div class="test-page p-6 max-w-4xl mx-auto">
    <h1 class="text-2xl font-bold mb-6">简单插件测试</h1>
    
    <div class="mb-4">
      <h2 class="text-lg font-semibold mb-2">测试内容</h2>
      <div class="border border-gray-300 rounded p-4 bg-white">
        <AiResponseRenderer :content="testContent" />
      </div>
    </div>

    <div class="mt-6 p-4 bg-blue-50 rounded-lg">
      <h3 class="font-semibold mb-2">插件状态</h3>
      <p class="text-sm text-gray-700">
        如果您看到下面的代码有语法高亮，说明插件工作正常。
      </p>
    </div>
  </div>
</template>

<script setup lang="ts">
import AiResponseRenderer from './components/AiResponseRenderer/index.vue'

const testContent = ref(`# 插件测试

## 代码高亮测试

\`\`\`javascript
function hello() {
  console.log('Hello World!');
  return 'success';
}
\`\`\`

## 基本格式

- **粗体**
- *斜体*
- \`内联代码\`

## 表格

| 插件 | 状态 |
|------|------|
| marked | ✅ |
| highlight.js | ✅ |

如果以上内容正常显示，说明插件化实现成功！`)
</script>

<style scoped>
.test-page {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
}
</style>
