<template>
  <div class="test-page p-6 max-w-4xl mx-auto">
    <h1 class="text-2xl font-bold mb-6">标准 Markdown 输出测试</h1>
    
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <!-- 原始内容 -->
      <div class="space-y-4">
        <h2 class="text-lg font-semibold">原始 Markdown</h2>
        <textarea 
          v-model="testContent"
          class="w-full h-96 p-3 border border-gray-300 rounded font-mono text-sm"
        />
      </div>

      <!-- 渲染结果 -->
      <div class="space-y-4">
        <h2 class="text-lg font-semibold">渲染结果</h2>
        <div class="border border-gray-300 rounded p-4 bg-white h-96 overflow-auto">
          <AiResponseRenderer :content="testContent" />
        </div>
      </div>
    </div>

    <div class="mt-6 p-4 bg-blue-50 rounded-lg">
      <h3 class="font-semibold mb-2">说明</h3>
      <p class="text-sm text-gray-700">
        这个版本使用标准的 GitHub Flavored Markdown 渲染，输出应该与标准的 Markdown 渲染器一致。
      </p>
    </div>
  </div>
</template>

<script setup lang="ts">
import AiResponseRenderer from './components/AiResponseRenderer/index.vue'

const testContent = ref(`# 标准 Markdown 测试

这是一个**标准的 Markdown**文档，用来测试渲染效果。

## 基本格式

- **粗体文本**
- *斜体文本*
- \`内联代码\`
- [链接](https://example.com)

## 代码块

\`\`\`javascript
function hello() {
  console.log('Hello World!');
}
\`\`\`

## 表格

| 列1 | 列2 | 列3 |
|-----|-----|-----|
| A   | B   | C   |
| 1   | 2   | 3   |

## 引用

> 这是一个引用块
> 包含多行内容

## 列表

1. 第一项
2. 第二项
   - 嵌套项
   - 另一个嵌套项
3. 第三项

---

这就是标准的 Markdown 渲染效果。`)
</script>

<style scoped>
.test-page {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
}
</style>
