<template>
  <div :class="`ai-response-container max-w-4xl ${fontSizeClass}`">
    <div v-if="hasError" class="render-error">
      渲染内容时发生错误，请检查内容格式。
    </div>
    <div v-else v-html="renderedContent" />
  </div>
</template>

<script setup lang="ts">
interface Props {
  content: string
  fontSize?: 'sm' | 'base' | 'lg'
}

const props = withDefaults(defineProps<Props>(), {
  fontSize: 'base'
})

// 响应式数据
const renderedContent = ref('')
const hasError = ref(false)

// Emoji 处理工具函数
const processEmojis = (text: string): string => {
  const emojiMap: { [key: string]: string } = {
    // 基础表情符号
    '☺': '☺️',
    ':)': '😊',
    ':-)': '😊',
    ':(': '😢',
    ':-(': '😢',
    ':D': '😃',
    ':-D': '😃',
    ';)': '😉',
    ';-)': '😉',
    ':P': '😛',
    ':-P': '😛',
    ':p': '😛',
    ':-p': '😛',
    ':o': '😮',
    ':-o': '😮',
    ':O': '😱',
    ':-O': '😱',
    ':|': '😐',
    ':-|': '😐',
    ':*': '😘',
    ':-*': '😘',
    '<3': '❤️',
    '</3': '💔',
    '~': '～',
    '。。。': '…',
    '...': '…'
  }

  let processedText = text

  Object.entries(emojiMap).forEach(([textEmoji, emoji]) => {
    const escapedText = textEmoji.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')

    if (textEmoji.match(/^[:\-\(\)\[\]<>3pPdDoO\|*]+$/)) {
      const parts = processedText.split(textEmoji)
      if (parts.length > 1) {
        processedText = parts.join(emoji)
      }
    } else if (textEmoji === '~') {
      processedText = processedText.replace(/~(?=\s|$)/g, emoji)
    } else if (textEmoji === '。。。' || textEmoji === '...') {
      const regex = new RegExp(escapedText, 'g')
      processedText = processedText.replace(regex, emoji)
    }
  })

  return processedText
}

// 清理内容函数
const cleanContent = (text: string): string => {
  let cleaned = text.trim()

  // 处理表格前的大量空行
  cleaned = cleaned.replace(/(\n\s*){100,}(\|)/g, '\n\n$2')
  cleaned = cleaned.replace(/(\n\s*){50,}(\|)/g, '\n\n$2')
  cleaned = cleaned.replace(/(\n\s*){30,}(\|)/g, '\n\n$2')
  cleaned = cleaned.replace(/(\n\s*){20,}(\|)/g, '\n\n$2')
  cleaned = cleaned.replace(/(\n\s*){10,}(\|)/g, '\n\n$2')
  cleaned = cleaned.replace(/(\n\s*){5,}(\|)/g, '\n\n$2')
  cleaned = cleaned.replace(/(\n\s*){3,}(\|)/g, '\n\n$2')

  // 处理大量空白字符
  cleaned = cleaned.replace(/\s{20,}/g, ' ')
  cleaned = cleaned.replace(/\s{10,}/g, ' ')

  // 常规清理
  cleaned = cleaned
    .replace(/\n{5,}/g, '\n\n')
    .replace(/\n{3,}/g, '\n\n')
    .replace(/^\s*\n/gm, '\n')
    .replace(/\n\s*$/gm, '\n')
    .replace(/\n\s+\n/g, '\n\n')
    .replace(/(\|.*\|)\n+/g, '$1\n')

  cleaned = cleaned.replace(/\n{2,}(\|)/g, '\n\n$1')

  return cleaned
}

// 字体大小类名
const fontSizeClass = computed(() => {
  return props.fontSize === 'lg' ? 'text-base' : props.fontSize === 'base' ? 'text-sm' : 'text-xs'
})

// 渲染Mermaid图表
const renderMermaidDiagrams = () => {
  if (!process.client || !window.mermaid) return

  const mermaid = window.mermaid

  mermaid.initialize({
    startOnLoad: false,
    theme: 'default',
    securityLevel: 'loose',
    fontFamily: 'inherit'
  })

  const containers = document.querySelectorAll('.mermaid-container[data-mermaid]')

  for (const container of containers) {
    const code = decodeURIComponent(container.getAttribute('data-mermaid') || '')
    const id = container.id

    try {
      mermaid.render(id, code).then(({ svg }) => {
        container.innerHTML = svg
      }).catch(error => {
        console.error('Mermaid render error:', error)
        container.innerHTML = `
          <div class="mermaid-error">
            <p>Failed to render diagram</p>
          </div>
        `
      })
    } catch (error) {
      console.error('Mermaid render error:', error)
      container.innerHTML = `
        <div class="mermaid-error">
          <p>Failed to render diagram</p>
        </div>
      `
    }
  }
}

// 渲染LaTeX块
const renderLatexBlocks = () => {
  if (!process.client || !window.katex) return

  const katex = window.katex

  const containers = document.querySelectorAll('.latex-block[data-latex]')

  for (const container of containers) {
    const code = decodeURIComponent(container.getAttribute('data-latex') || '')

    try {
      const html = katex.renderToString(code.trim(), { displayMode: true })
      container.innerHTML = html
    } catch (error) {
      console.error('LaTeX render error:', error)
      container.innerHTML = `
        <div class="latex-error">
          <p>LaTeX syntax error</p>
        </div>
      `
    }
  }
}

// 渲染内容
const renderContent = () => {
  if (!process.client) {
    renderedContent.value = props.content
    return
  }

  try {
    hasError.value = false

    // 简化版本：直接使用基础的markdown渲染
    // 避免动态导入问题
    let content = cleanContent(props.content)
    content = processEmojis(content)

    // 基础的markdown转换
    content = convertBasicMarkdown(content)

    renderedContent.value = content

  } catch (error) {
    console.error('Failed to render content:', error)
    hasError.value = true
    renderedContent.value = props.content
  }
}

// 表格转换函数
const convertTables = (text: string): string => {
  // 匹配markdown表格
  const tableRegex = /(\|.*\|[\r\n]+\|[-\s|:]+\|[\r\n]+((\|.*\|[\r\n]*)+))/gm

  return text.replace(tableRegex, (match) => {
    const lines = match.trim().split('\n').map(line => line.trim())

    if (lines.length < 3) return match

    const headerLine = lines[0]
    const separatorLine = lines[1]
    const dataLines = lines.slice(2)

    // 解析表头
    const headers = headerLine.split('|').map(cell => cell.trim()).filter(cell => cell)

    // 解析数据行
    const rows = dataLines.map(line => {
      return line.split('|').map(cell => cell.trim()).filter(cell => cell)
    }).filter(row => row.length > 0)

    // 生成HTML表格
    let tableHtml = '<div class="table-container overflow-x-auto my-2 md:my-4 -mx-2 md:mx-0">'
    tableHtml += '<table class="min-w-full text-xs md:text-sm border-collapse">'

    // 表头
    if (headers.length > 0) {
      tableHtml += '<thead class="bg-gray-50"><tr>'
      headers.forEach(header => {
        tableHtml += `<th class="border border-gray-200 px-2 md:px-3 py-1 md:py-2 text-left font-medium text-gray-700 text-xs md:text-sm">${header}</th>`
      })
      tableHtml += '</tr></thead>'
    }

    // 表体
    if (rows.length > 0) {
      tableHtml += '<tbody>'
      rows.forEach(row => {
        tableHtml += '<tr>'
        row.forEach(cell => {
          tableHtml += `<td class="border border-gray-200 px-2 md:px-3 py-1 md:py-2 text-gray-600 text-xs md:text-sm">${cell}</td>`
        })
        tableHtml += '</tr>'
      })
      tableHtml += '</tbody>'
    }

    tableHtml += '</table></div>'

    return tableHtml
  })
}

// 基础markdown转换函数
const convertBasicMarkdown = (text: string): string => {
  let html = text

  // 先处理代码块，避免其内容被其他规则影响
  html = html.replace(/```(\w+)?\n([\s\S]*?)```/g, (match, lang, code) => {
    if (lang === 'mermaid') {
      const id = `mermaid-${Math.random().toString(36).substring(2, 11)}`
      return `<div class="mermaid-container" data-mermaid="${encodeURIComponent(code.trim())}" id="${id}">
        <div class="mermaid-loading">Rendering diagram...</div>
      </div>`
    }

    if (lang === 'latex' || lang === 'tex') {
      return `<div class="latex-block" data-latex="${encodeURIComponent(code.trim())}">
        <div class="latex-loading">Rendering LaTeX...</div>
      </div>`
    }

    return `<div class="code-block-wrapper"><pre class="hljs"><code class="hljs language-${lang || 'plaintext'}">${escapeHtml(code.trim())}</code></pre></div>`
  })

  // 转换标题
  html = html.replace(/^### (.*$)/gim, '<h3 class="text-sm md:text-lg font-medium mt-3 md:mt-4 mb-1.5 md:mb-2 text-gray-800">$1</h3>')
  html = html.replace(/^## (.*$)/gim, '<h2 class="text-base md:text-xl font-medium md:font-semibold mt-3 md:mt-5 mb-2 md:mb-3 text-gray-800">$1</h2>')
  html = html.replace(/^# (.*$)/gim, '<h1 class="text-lg md:text-2xl font-semibold md:font-bold mt-4 md:mt-6 mb-2 md:mb-4 text-gray-900">$1</h1>')

  // 转换引用块
  html = html.replace(/^> (.+)$/gm, '<blockquote class="border-l-2 md:border-l-4 border-blue-200 pl-3 md:pl-4 py-1 md:py-2 my-2 md:my-4 bg-blue-50 text-sm md:text-base text-gray-600 italic">$1</blockquote>')

  // 转换列表项
  html = html.replace(/^\* (.+)$/gm, '<li class="leading-5 md:leading-6 mb-1 md:mb-1.5">$1</li>')

  // 将连续的li包装在ul中
  html = html.replace(/(<li[^>]*>.*?<\/li>\s*)+/gs, (match) => {
    return `<ul class="list-disc list-inside my-2 md:my-3 ml-2 md:ml-4 space-y-1 md:space-y-1.5 text-sm md:text-base text-gray-700">${match}</ul>`
  })

  // 转换粗体和斜体
  html = html.replace(/\*\*(.*?)\*\*/g, '<strong class="font-semibold text-gray-900">$1</strong>')
  html = html.replace(/\*(.*?)\*/g, '<em class="italic text-gray-700">$1</em>')

  // 转换内联代码
  html = html.replace(/`([^`]+)`/g, '<code class="inline-code bg-gray-100 text-gray-800 px-1 md:px-1.5 py-0.5 rounded text-xs md:text-sm font-mono">$1</code>')

  // 转换表格
  html = convertTables(html)

  // 转换链接
  html = html.replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<a href="$2" class="text-blue-600 hover:text-blue-800 underline text-sm md:text-base break-words" target="_blank" rel="noopener noreferrer">$1</a>')

  // 分割成行，处理段落
  const lines = html.split('\n')
  const processedLines = lines.map(line => {
    const trimmedLine = line.trim()

    // 跳过空行
    if (!trimmedLine) return ''

    // 跳过已经是HTML标签的行
    if (trimmedLine.startsWith('<') && trimmedLine.endsWith('>')) {
      return trimmedLine
    }

    // 跳过已经包含HTML标签的行
    if (trimmedLine.includes('<') && trimmedLine.includes('>')) {
      return trimmedLine
    }

    // 将普通文本行包装为段落
    return `<p class="text-sm md:text-base leading-6 md:leading-7 my-2 md:my-3 text-gray-700">${trimmedLine}</p>`
  })

  // 过滤空行并连接，添加br标签但不占用高度
  html = processedLines.filter(line => line.trim()).join('<br class="no-height">')

  return html
}

// HTML转义函数
const escapeHtml = (text: string): string => {
  const div = document.createElement('div')
  div.textContent = text
  return div.innerHTML
}

// 使用CDN版本的库进行高级渲染
const renderWithCDNLibs = async () => {
  if (!process.client) return

  try {
    // 等待CDN库加载完成
    await waitForCDNLibs()

    // 使用全局的marked库
    if (window.marked) {
      const content = cleanContent(props.content)
      const processedContent = processEmojis(content)
      renderedContent.value = window.marked.parse(processedContent)

      // 渲染特殊内容
      nextTick(() => {
        renderMermaidDiagrams()
        renderLatexBlocks()
      })
    }
  } catch (error) {
    console.error('Failed to render with CDN libs:', error)
  }
}

// 等待CDN库加载
const waitForCDNLibs = (): Promise<void> => {
  return new Promise((resolve) => {
    const checkLibs = () => {
      if (window.marked && window.mermaid && window.katex) {
        resolve()
      } else {
        setTimeout(checkLibs, 100)
      }
    }
    checkLibs()
  })
}





// 初始化渲染
onMounted(() => {
  // 优先尝试使用CDN版本
  if (window.marked) {
    renderWithCDNLibs()
  } else {
    renderContent()
    // 如果CDN库后续加载完成，再次渲染
    setTimeout(() => {
      if (window.marked) {
        renderWithCDNLibs()
      }
    }, 1000)
  }
})

// 监听内容变化，重新渲染
watch(() => props.content, () => {
  if (window.marked) {
    renderWithCDNLibs()
  } else {
    renderContent()
  }
})
</script>

<style scoped>
/* AI响应容器基础样式 */
.ai-response-container {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
  line-height: 1.6;
  color: #374151;
}

/* 代码高亮样式 */
:deep(.hljs) {
  background: #1e1e1e !important;
  color: #d4d4d4 !important;
  padding: 1rem;
  border-radius: 0.5rem;
  overflow-x: auto;
  margin: 0.5rem 0;
  font-family: 'Fira Code', 'Monaco', 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
  font-size: 0.875rem;
  line-height: 1.5;
}

/* 内联代码样式 */
:deep(.inline-code) {
  font-family: 'Fira Code', 'Monaco', 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
  background-color: #f3f4f6;
  color: #1f2937;
  padding: 0.125rem 0.375rem;
  border-radius: 0.25rem;
  font-size: 0.875em;
  font-weight: 500;
}

/* 段落样式 */
:deep(p) {
  margin: 0.75rem 0;
  line-height: 1.7;
  color: #374151;
}

/* 标题样式 */
:deep(h1) {
  font-size: 1.5rem;
  font-weight: 700;
  margin: 1.5rem 0 1rem 0;
  color: #111827;
  line-height: 1.3;
}

:deep(h2) {
  font-size: 1.25rem;
  font-weight: 600;
  margin: 1.25rem 0 0.75rem 0;
  color: #1f2937;
  line-height: 1.4;
}

:deep(h3) {
  font-size: 1.125rem;
  font-weight: 600;
  margin: 1rem 0 0.5rem 0;
  color: #1f2937;
  line-height: 1.4;
}

/* 列表样式 */
:deep(ul), :deep(ol) {
  margin: 0.75rem 0;
  padding-left: 1.5rem;
}

:deep(li) {
  margin: 0.25rem 0;
  line-height: 1.6;
}

/* 引用块样式 */
:deep(blockquote) {
  border-left: 4px solid #3b82f6;
  background-color: #eff6ff;
  padding: 0.75rem 1rem;
  margin: 1rem 0;
  font-style: italic;
  color: #1e40af;
  border-radius: 0 0.375rem 0.375rem 0;
}

/* 链接样式 */
:deep(a) {
  color: #2563eb;
  text-decoration: underline;
  transition: color 0.2s ease;
}

:deep(a:hover) {
  color: #1d4ed8;
}

/* 强调文本样式 */
:deep(strong) {
  font-weight: 600;
  color: #111827;
}

:deep(em) {
  font-style: italic;
  color: #4b5563;
}

/* 表格样式 */
:deep(.table-container) {
  border-radius: 0.5rem;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  margin: 1rem 0;
}

:deep(table) {
  width: 100%;
  border-collapse: collapse;
  font-size: 0.875rem;
}

:deep(th), :deep(td) {
  padding: 0.75rem;
  text-align: left;
  border-bottom: 1px solid #e5e7eb;
}

:deep(th) {
  background-color: #f9fafb;
  font-weight: 600;
  color: #374151;
}

/* 图片样式 */
:deep(.image-container) {
  margin: 1rem 0;
  text-align: center;
}

:deep(.image-container img) {
  max-width: 100%;
  height: auto;
  border-radius: 0.5rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

:deep(.image-caption) {
  margin-top: 0.5rem;
  font-size: 0.875rem;
  color: #6b7280;
  font-style: italic;
}

/* Mermaid样式 */
:deep(.mermaid-container) {
  margin: 1.5rem 0;
  text-align: center;
  background-color: #fafafa;
  border-radius: 0.5rem;
  padding: 1rem;
  border: 1px solid #e5e7eb;
}

:deep(.mermaid-loading) {
  padding: 2rem;
  color: #6b7280;
  font-style: italic;
  font-size: 0.875rem;
}

:deep(.mermaid-error) {
  background-color: #fef2f2;
  color: #dc2626;
  padding: 1rem;
  border-radius: 0.5rem;
  text-align: left;
  border: 1px solid #fecaca;
  font-size: 0.875rem;
}

/* LaTeX样式 */
:deep(.latex-block) {
  text-align: center;
  margin: 1.5rem 0;
  background-color: #fafafa;
  border-radius: 0.5rem;
  padding: 1rem;
  border: 1px solid #e5e7eb;
}

:deep(.latex-loading) {
  padding: 1rem;
  color: #6b7280;
  font-style: italic;
  font-size: 0.875rem;
}

:deep(.latex-error) {
  background-color: #fef2f2;
  color: #dc2626;
  padding: 1rem;
  border-radius: 0.5rem;
  border: 1px solid #fecaca;
  font-size: 0.875rem;
}

/* KaTeX渲染后的样式 */
:deep(.katex-display) {
  margin: 1rem 0;
  text-align: center;
}

:deep(.katex) {
  font-size: 1.1em;
}

/* 分割线样式 */
:deep(hr) {
  border: none;
  border-top: 1px solid #e5e7eb;
  margin: 1.5rem 0;
}

/* 无高度的br标签 */
:deep(br.no-height) {
  line-height: 0;
  height: 0;
  margin: 0;
  padding: 0;
  display: inline;
}

/* 错误样式 */
.render-error {
  background-color: #fef2f2;
  color: #dc2626;
  padding: 1rem;
  border-radius: 0.5rem;
  margin: 0.5rem 0;
  border: 1px solid #fecaca;
  font-size: 0.875rem;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .ai-response-container {
    font-size: 0.875rem;
  }

  :deep(.hljs) {
    padding: 0.75rem;
    font-size: 0.8rem;
  }

  :deep(.table-container) {
    margin-left: -1rem;
    margin-right: -1rem;
    font-size: 0.8rem;
  }

  :deep(h1) {
    font-size: 1.25rem;
  }

  :deep(h2) {
    font-size: 1.125rem;
  }

  :deep(h3) {
    font-size: 1rem;
  }

  :deep(p) {
    font-size: 0.875rem;
    line-height: 1.6;
  }

  :deep(.mermaid-container),
  :deep(.latex-block) {
    margin-left: -0.5rem;
    margin-right: -0.5rem;
  }
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* 打印样式 */
@media print {
  .ai-response-container {
    color: #000;
  }

  :deep(.hljs) {
    background: #f8f9fa !important;
    color: #000 !important;
    border: 1px solid #dee2e6;
  }

  :deep(.mermaid-container),
  :deep(.latex-block) {
    break-inside: avoid;
  }
}
</style>
