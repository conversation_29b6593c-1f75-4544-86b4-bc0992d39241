<template>
  <div class="test-page p-6 max-w-6xl mx-auto">
    <h1 class="text-3xl font-bold mb-6">增强版 AiResponseRenderer 测试</h1>
    
    <div class="mb-6">
      <label class="block text-sm font-medium mb-2">选择测试内容：</label>
      <select 
        v-model="selectedTest" 
        class="border border-gray-300 rounded px-3 py-2 w-full max-w-xs"
      >
        <option value="comprehensive">综合测试</option>
        <option value="tables">表格测试</option>
        <option value="code">代码测试</option>
        <option value="mixed">混合内容</option>
        <option value="typography">排版测试</option>
      </select>
    </div>

    <div class="grid grid-cols-1 xl:grid-cols-2 gap-6">
      <!-- 原始内容 -->
      <div class="space-y-4">
        <h2 class="text-xl font-semibold">原始 Markdown</h2>
        <textarea 
          v-model="testContents[selectedTest]"
          class="w-full h-96 p-3 border border-gray-300 rounded font-mono text-sm resize-none"
          readonly
        />
      </div>

      <!-- 渲染结果 -->
      <div class="space-y-4">
        <h2 class="text-xl font-semibold">渲染结果</h2>
        <div class="border border-gray-300 rounded p-4 bg-white h-96 overflow-auto">
          <AiResponseRenderer :content="testContents[selectedTest]" />
        </div>
      </div>
    </div>

    <!-- 对比说明 -->
    <div class="mt-8 p-4 bg-green-50 rounded-lg">
      <h3 class="text-lg font-semibold mb-2 text-green-800">改进特性</h3>
      <ul class="text-sm space-y-1 text-green-700">
        <li>✅ 更好的标题层次和间距</li>
        <li>✅ 改进的代码块样式和高亮</li>
        <li>✅ 更美观的表格设计</li>
        <li>✅ 优化的段落和行间距</li>
        <li>✅ 增强的列表和引用块样式</li>
        <li>✅ 更好的 HTML 和 Markdown 混合支持</li>
      </ul>
    </div>
  </div>
</template>

<script setup lang="ts">
import AiResponseRenderer from './components/AiResponseRenderer/index.vue'

const selectedTest = ref('comprehensive')

const testContents = reactive({
  comprehensive: `# 综合 Markdown 测试

这是一个**综合测试**文档，展示各种 Markdown 语法的渲染效果。

## 文本格式

这里有 **粗体文本**、*斜体文本*、***粗斜体文本*** 和 ~~删除线文本~~。

还有 \`内联代码\` 和 [链接](https://example.com)。

## 列表

### 无序列表
- 第一项
- 第二项
  - 嵌套项 1
  - 嵌套项 2
- 第三项

### 有序列表
1. 第一步
2. 第二步
3. 第三步

## 引用

> 这是一个引用块。
> 
> 它可以包含多个段落，并且支持其他 Markdown 语法，比如 **粗体** 和 *斜体*。

## 代码

\`\`\`javascript
function greet(name) {
  console.log(\`Hello, \${name}!\`);
  return \`Welcome, \${name}\`;
}

// 调用函数
greet('World');
\`\`\`

## 表格

| 功能 | 支持 | 说明 |
|------|------|------|
| 标题 | ✅ | 支持 H1-H6 |
| 列表 | ✅ | 有序和无序 |
| 代码 | ✅ | 内联和块级 |
| 表格 | ✅ | 支持对齐 |

---

这就是综合测试的内容！`,

  tables: `# 表格测试

## 基础表格

| 姓名 | 年龄 | 职业 |
|------|------|------|
| 张三 | 25 | 工程师 |
| 李四 | 30 | 设计师 |
| 王五 | 28 | 产品经理 |

## 对齐表格

| 左对齐 | 居中对齐 | 右对齐 |
|:-------|:-------:|-------:|
| 内容1 | 内容2 | 内容3 |
| 较长的内容 | 中等 | 短 |
| A | B | C |

## 复杂表格

| 功能特性 | 状态 | 优先级 | 负责人 | 预计完成时间 |
|----------|:----:|:------:|--------|:------------:|
| 用户登录 | ✅ 完成 | 高 | 张三 | 2024-01-15 |
| 数据导出 | 🔄 进行中 | 中 | 李四 | 2024-01-20 |
| 权限管理 | ⏳ 待开始 | 高 | 王五 | 2024-01-25 |
| 报表生成 | ❌ 暂停 | 低 | 赵六 | TBD |`,

  code: `# 代码测试

## JavaScript 代码

\`\`\`javascript
// ES6 类示例
class Calculator {
  constructor() {
    this.result = 0;
  }
  
  add(num) {
    this.result += num;
    return this;
  }
  
  multiply(num) {
    this.result *= num;
    return this;
  }
  
  getResult() {
    return this.result;
  }
}

// 使用示例
const calc = new Calculator();
const result = calc.add(5).multiply(3).getResult();
console.log(result); // 15
\`\`\`

## Python 代码

\`\`\`python
def fibonacci(n):
    """生成斐波那契数列"""
    if n <= 1:
        return n
    return fibonacci(n-1) + fibonacci(n-2)

# 生成前10个斐波那契数
for i in range(10):
    print(f"F({i}) = {fibonacci(i)}")
\`\`\`

## CSS 代码

\`\`\`css
/* 现代卡片样式 */
.card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  padding: 1.5rem;
  transition: transform 0.2s ease;
}

.card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px -5px rgba(0, 0, 0, 0.1);
}
\`\`\`

## 内联代码

在 JavaScript 中，可以使用 \`const\` 声明常量，使用 \`let\` 声明变量。

数组方法如 \`map()\`、\`filter()\` 和 \`reduce()\` 是函数式编程的核心。`,

  mixed: `# HTML 和 Markdown 混合内容

这是一个展示 **HTML** 和 Markdown 混合使用的示例。

<div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; border-radius: 10px; margin: 20px 0;">
  <h2 style="margin: 0 0 10px 0;">🎨 自定义样式容器</h2>
  <p style="margin: 0;">这是一个使用 HTML 和内联样式的容器，展示了混合内容的能力。</p>
</div>

## Markdown 继续

回到 Markdown 语法：

- 这是一个 **Markdown 列表**
- 包含 *斜体* 和 \`代码\`

<table style="width: 100%; border-collapse: collapse; margin: 20px 0;">
  <thead style="background: #f8f9fa;">
    <tr>
      <th style="border: 1px solid #dee2e6; padding: 12px;">HTML 表格</th>
      <th style="border: 1px solid #dee2e6; padding: 12px;">Markdown 表格</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td style="border: 1px solid #dee2e6; padding: 12px;">更灵活的样式</td>
      <td style="border: 1px solid #dee2e6; padding: 12px;">更简洁的语法</td>
    </tr>
    <tr>
      <td style="border: 1px solid #dee2e6; padding: 12px;">需要更多代码</td>
      <td style="border: 1px solid #dee2e6; padding: 12px;">自动格式化</td>
    </tr>
  </tbody>
</table>

### 代码示例

\`\`\`vue
<template>
  <div class="mixed-content">
    <h1>{{ title }}</h1>
    <div v-html="htmlContent" />
  </div>
</template>
\`\`\`

<div style="background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; margin: 15px 0;">
  <strong>💡 提示：</strong> 这个组件可以无缝处理 HTML 和 Markdown 的混合内容！
</div>`,

  typography: `# 排版测试

## 各级标题展示

# 一级标题 (H1)
## 二级标题 (H2)
### 三级标题 (H3)
#### 四级标题 (H4)
##### 五级标题 (H5)
###### 六级标题 (H6)

## 段落和间距

这是第一个段落。它包含了一些文本来展示段落的渲染效果。段落之间应该有适当的间距，使内容易于阅读。

这是第二个段落。注意段落之间的间距。这个段落稍微长一些，用来测试长段落的渲染效果和行高设置。

## 文本格式组合

这里展示各种文本格式的组合：

- **粗体文本** 用于强调重要内容
- *斜体文本* 用于表示引用或特殊术语
- ***粗斜体文本*** 用于极度强调
- ~~删除线文本~~ 用于表示已删除的内容
- \`内联代码\` 用于表示代码片段
- [链接文本](https://example.com) 用于跳转

## 复杂列表

### 多层嵌套列表

1. 第一级有序列表
   - 第二级无序列表
   - 另一个第二级项目
     1. 第三级有序列表
     2. 第三级第二项
        - 第四级无序列表
        - 第四级第二项
2. 第一级第二项
3. 第一级第三项

### 列表中的格式

- **粗体项目**：这是一个粗体的列表项
- *斜体项目*：这是一个斜体的列表项
- \`代码项目\`：这是包含代码的列表项
- [链接项目](https://example.com)：这是包含链接的列表项

## 引用块测试

> 这是一个简单的引用块。

> 这是一个更长的引用块，用来测试多行引用的渲染效果。
> 
> 引用块可以包含多个段落，每个段落都应该正确缩进和格式化。
> 
> 引用块中也可以包含 **粗体**、*斜体* 和 \`代码\` 等格式。

## 分割线

上面的内容

---

下面的内容

***

更多内容`
})
</script>

<style scoped>
.test-page {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
}

textarea {
  font-family: 'JetBrains Mono', 'Fira Code', 'Monaco', monospace;
}
</style>
