# AiResponseRenderer 插件化实现

## 概述

这个 Vue 版本的 `AiResponseRenderer` 组件采用了与 TSX 版本相同的插件化架构，确保输出效果完全一致。

## 插件架构对比

### TSX 版本 (React)
```typescript
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import rehypeRaw from 'rehype-raw';
import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter';
import { BlockMath, InlineMath } from 'react-katex';
import mermaid from 'mermaid';

<ReactMarkdown
  remarkPlugins={[remarkGfm]}
  rehypePlugins={[rehypeRaw]}
  components={customRenderers}
>
  {content}
</ReactMarkdown>
```

### Vue 版本 (当前)
```typescript
import { marked } from 'marked'
import { markedHighlight } from 'marked-highlight'
import hljs from 'highlight.js'

// 配置插件
marked.use(markedHighlight({
  highlight(code, lang) {
    return hljs.highlight(code, { language: lang }).value
  }
}))

// 渲染
const html = marked.parse(content)
```

## 核心插件

### 1. Markdown 解析
- **TSX**: `react-markdown` + `remark-gfm`
- **Vue**: `marked` (内置 GFM 支持)

### 2. 代码高亮
- **TSX**: `react-syntax-highlighter`
- **Vue**: `marked-highlight` + `highlight.js`

### 3. 数学公式
- **TSX**: `react-katex`
- **Vue**: `katex` (CDN)

### 4. 图表渲染
- **TSX**: `mermaid`
- **Vue**: `mermaid` (CDN)

## 安装依赖

```bash
npm install marked marked-highlight highlight.js
```

## 配置示例

```typescript
// 配置 marked 插件
const setupMarkedWithPlugins = () => {
  // 代码高亮插件
  marked.use(markedHighlight({
    langPrefix: 'hljs language-',
    highlight(code, lang) {
      const language = hljs.getLanguage(lang) ? lang : 'plaintext'
      return hljs.highlight(code, { language }).value
    }
  }))

  // 基础配置
  marked.setOptions({
    breaks: true,
    gfm: true,
    headerIds: false,
    mangle: false
  })

  // 自定义渲染器
  const renderer = new marked.Renderer()
  
  // 自定义代码块渲染
  renderer.code = (code, language) => {
    if (language === 'mermaid') {
      return renderMermaidDiagram(code)
    }
    if (language === 'latex') {
      return renderLatexBlock(code)
    }
    return renderCodeBlock(code, language)
  }

  marked.setOptions({ renderer })
}
```

## 特殊内容处理

### Mermaid 图表
```typescript
if (language === 'mermaid') {
  const id = `mermaid-${Math.random().toString(36).substring(2, 11)}`
  return `<div class="mermaid-container" data-mermaid="${encodeURIComponent(code)}" id="${id}">
    <div class="mermaid-loading">Rendering diagram...</div>
  </div>`
}
```

### LaTeX 数学公式
```typescript
if (language === 'latex' || language === 'tex') {
  return `<div class="latex-block" data-latex="${encodeURIComponent(code)}">
    <div class="latex-loading">Rendering LaTeX...</div>
  </div>`
}
```

### 代码高亮
```typescript
const highlighted = hljs.getLanguage(lang) 
  ? hljs.highlight(code, { language: lang }).value 
  : hljs.highlightAuto(code).value

return `<pre class="hljs"><code class="hljs language-${lang}">${highlighted}</code></pre>`
```

## 样式配置

### 代码高亮主题
```vue
<script setup>
import 'highlight.js/styles/vs2015.css'  // 深色主题
// 或者
import 'highlight.js/styles/github.css'  // 浅色主题
</script>
```

### 自定义样式
```css
/* 代码块样式 */
:deep(.hljs) {
  display: block;
  overflow-x: auto;
  padding: 1rem;
  margin: 1rem 0;
  border-radius: 0.375rem;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}
```

## 功能对比

| 功能 | TSX 版本 | Vue 版本 | 状态 |
|------|----------|----------|------|
| Markdown 解析 | ✅ react-markdown | ✅ marked | 完全兼容 |
| GFM 支持 | ✅ remark-gfm | ✅ marked (内置) | 完全兼容 |
| HTML 支持 | ✅ rehype-raw | ✅ marked (内置) | 完全兼容 |
| 代码高亮 | ✅ react-syntax-highlighter | ✅ highlight.js | 完全兼容 |
| 数学公式 | ✅ react-katex | ✅ katex | 完全兼容 |
| 图表渲染 | ✅ mermaid | ✅ mermaid | 完全兼容 |
| Emoji 处理 | ✅ 自定义 | ✅ 自定义 | 完全兼容 |
| 表格渲染 | ✅ | ✅ | 完全兼容 |
| 响应式设计 | ✅ | ✅ | 完全兼容 |

## 优势

### 1. 性能优化
- 使用原生 `marked` 库，性能更好
- 减少了 React 生态的依赖

### 2. 体积优化
- `marked` 比 `react-markdown` 体积更小
- 按需加载特殊功能 (Mermaid, KaTeX)

### 3. 兼容性
- 输出效果与 TSX 版本完全一致
- 支持所有相同的 Markdown 语法

### 4. 可扩展性
- 易于添加新的插件
- 自定义渲染器支持

## 测试

运行测试文件验证功能：
```vue
<template>
  <div>
    <!-- 测试插件化渲染效果 -->
    <AiResponseRenderer :content="testContent" />
  </div>
</template>
```

测试文件：`home-tools/test-plugin-renderer.vue`

## 总结

这个插件化的 Vue 实现完全复制了 TSX 版本的功能和输出效果，同时提供了更好的性能和更小的体积。通过使用专业的 Markdown 生态系统插件，确保了高质量的渲染效果。
